{"__meta": {"id": "01JZ907FQ5GHCKM424NXSKM3NK", "datetime": "2025-07-03 20:39:10", "utime": **********.309607, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.447804, "end": **********.309622, "duration": 0.8618180751800537, "duration_str": "862ms", "measures": [{"label": "Booting", "start": **********.447804, "relative_start": 0, "end": **********.509743, "relative_end": **********.509743, "duration": 0.*****************, "duration_str": "61.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.509759, "relative_start": 0.*****************, "end": **********.309624, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "800ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.512769, "relative_start": 0.*****************, "end": **********.513525, "relative_end": **********.513525, "duration": 0.0007560253143310547, "duration_str": "756μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.950136, "relative_start": 0.****************, "end": **********.309203, "relative_end": **********.309203, "duration": 0.****************, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: content.dashboard.dashboards-analytics", "start": **********.171171, "relative_start": 0.****************, "end": **********.171171, "relative_end": **********.171171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.layoutMaster", "start": **********.21854, "relative_start": 0.****************, "end": **********.21854, "relative_end": **********.21854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.contentNavbarLayout", "start": **********.22021, "relative_start": 0.7724061012268066, "end": **********.22021, "relative_end": **********.22021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenuData", "start": **********.240919, "relative_start": 0.7931151390075684, "end": **********.240919, "relative_end": **********.240919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.verticalMenu", "start": **********.260097, "relative_start": 0.8122930526733398, "end": **********.260097, "relative_end": **********.260097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: _partials.macros", "start": **********.285598, "relative_start": 0.8377940654754639, "end": **********.285598, "relative_end": **********.285598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.menu.submenu", "start": **********.288784, "relative_start": 0.840980052947998, "end": **********.288784, "relative_end": **********.288784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.navbar.navbar", "start": **********.290354, "relative_start": 0.8425500392913818, "end": **********.290354, "relative_end": **********.290354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.footer.footer", "start": **********.294916, "relative_start": 0.8471119403839111, "end": **********.294916, "relative_end": **********.294916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.commonMaster", "start": **********.296394, "relative_start": 0.8485901355743408, "end": **********.296394, "relative_end": **********.296394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.styles", "start": **********.29778, "relative_start": 0.8499760627746582, "end": **********.29778, "relative_end": **********.29778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scriptsIncludes", "start": **********.300793, "relative_start": 0.8529889583587646, "end": **********.300793, "relative_end": **********.300793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.sections.scripts", "start": **********.304264, "relative_start": 0.8564600944519043, "end": **********.304264, "relative_end": **********.304264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 5333600, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "fr"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "content.dashboard.dashboards-analytics", "param_count": null, "params": [], "start": **********.171083, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.phpcontent.dashboard.dashboards-analytics", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Fcontent%2Fdashboard%2Fdashboards-analytics.blade.php&line=1", "ajax": false, "filename": "dashboards-analytics.blade.php", "line": "?"}}, {"name": "layouts.layoutMaster", "param_count": null, "params": [], "start": **********.218411, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/layoutMaster.blade.phplayouts.layoutMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FlayoutMaster.blade.php&line=1", "ajax": false, "filename": "layoutMaster.blade.php", "line": "?"}}, {"name": "layouts.contentNavbarLayout", "param_count": null, "params": [], "start": **********.220088, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/contentNavbarLayout.blade.phplayouts.contentNavbarLayout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcontentNavbarLayout.blade.php&line=1", "ajax": false, "filename": "contentNavbarLayout.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenuData", "param_count": null, "params": [], "start": **********.240802, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenuData.blade.phplayouts.sections.menu.verticalMenuData", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenuData.blade.php&line=1", "ajax": false, "filename": "verticalMenuData.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.verticalMenu", "param_count": null, "params": [], "start": **********.260011, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/verticalMenu.blade.phplayouts.sections.menu.verticalMenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2FverticalMenu.blade.php&line=1", "ajax": false, "filename": "verticalMenu.blade.php", "line": "?"}}, {"name": "_partials.macros", "param_count": null, "params": [], "start": **********.285406, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/_partials/macros.blade.php_partials.macros", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2F_partials%2Fmacros.blade.php&line=1", "ajax": false, "filename": "macros.blade.php", "line": "?"}}, {"name": "layouts.sections.menu.submenu", "param_count": null, "params": [], "start": **********.28868, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/menu/submenu.blade.phplayouts.sections.menu.submenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fmenu%2Fsubmenu.blade.php&line=1", "ajax": false, "filename": "submenu.blade.php", "line": "?"}}, {"name": "layouts.sections.navbar.navbar", "param_count": null, "params": [], "start": **********.29023, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/navbar/navbar.blade.phplayouts.sections.navbar.navbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fnavbar%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}}, {"name": "layouts.sections.footer.footer", "param_count": null, "params": [], "start": **********.294817, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/footer/footer.blade.phplayouts.sections.footer.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Ffooter%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "layouts.commonMaster", "param_count": null, "params": [], "start": **********.296308, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/commonMaster.blade.phplayouts.commonMaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2FcommonMaster.blade.php&line=1", "ajax": false, "filename": "commonMaster.blade.php", "line": "?"}}, {"name": "layouts.sections.styles", "param_count": null, "params": [], "start": **********.297691, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/styles.blade.phplayouts.sections.styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "layouts.sections.scriptsIncludes", "param_count": null, "params": [], "start": **********.300597, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scriptsIncludes.blade.phplayouts.sections.scriptsIncludes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2FscriptsIncludes.blade.php&line=1", "ajax": false, "filename": "scriptsIncludes.blade.php", "line": "?"}}, {"name": "layouts.sections.scripts", "param_count": null, "params": [], "start": **********.304174, "type": "blade", "hash": "bladeC:\\wamp64\\www\\recipe\\resources\\views/layouts/sections/scripts.blade.phplayouts.sections.scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fresources%2Fviews%2Flayouts%2Fsections%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 72, "nb_statements": 72, "nb_visible_statements": 72, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11916, "accumulated_duration_str": "119ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `manu_sessions` where `id` = 'H6ofDm7SW67K2M9V0u4VIwPstUUnmPZaf2FGs6IS' limit 1", "type": "query", "params": [], "bindings": ["H6ofDm7SW67K2M9V0u4VIwPstUUnmPZaf2FGs6IS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.5306718, "duration": 0.02655, "duration_str": "26.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "manufacturing", "explain": null, "start_percent": 0, "width_percent": 22.281}, {"sql": "select * from `manu_users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.563324, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "manufacturing", "explain": null, "start_percent": 22.281, "width_percent": 2.106}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-03'", "type": "query", "params": [], "bindings": ["2025-07-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.588347, "duration": 0.006809999999999999, "duration_str": "6.81ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:33", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=33", "ajax": false, "filename": "DashboardController.php", "line": "33"}, "connection": "manufacturing", "explain": null, "start_percent": 24.387, "width_percent": 5.715}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where `sale_date` >= '2025-06-30 00:00:00'", "type": "query", "params": [], "bindings": ["2025-06-30 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.60008, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=34", "ajax": false, "filename": "DashboardController.php", "line": "34"}, "connection": "manufacturing", "explain": null, "start_percent": 30.102, "width_percent": 0.604}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where `sale_date` >= '2025-07-01 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.605985, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=35", "ajax": false, "filename": "DashboardController.php", "line": "35"}, "connection": "manufacturing", "explain": null, "start_percent": 30.707, "width_percent": 0.629}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where `sale_date` between '2025-06-01 00:00:00' and '2025-07-01 00:00:00'", "type": "query", "params": [], "bindings": ["2025-06-01 00:00:00", "2025-07-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6101549, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:36", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=36", "ajax": false, "filename": "DashboardController.php", "line": "36"}, "connection": "manufacturing", "explain": null, "start_percent": 31.336, "width_percent": 0.462}, {"sql": "select sum(`total_amount`) as aggregate from `manu_purchases` where `purchase_date` >= '2025-07-01 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6151211, "duration": 0.00855, "duration_str": "8.55ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:41", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=41", "ajax": false, "filename": "DashboardController.php", "line": "41"}, "connection": "manufacturing", "explain": null, "start_percent": 31.798, "width_percent": 7.175}, {"sql": "select count(*) as aggregate from `manu_purchases` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.627188, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=42", "ajax": false, "filename": "DashboardController.php", "line": "42"}, "connection": "manufacturing", "explain": null, "start_percent": 38.973, "width_percent": 0.428}, {"sql": "select count(*) as aggregate from `manu_purchases` where `status` != 'completed' and `expected_delivery_date` < '2025-07-03 00:00:00'", "type": "query", "params": [], "bindings": ["completed", "2025-07-03 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.63192, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:44", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=44", "ajax": false, "filename": "DashboardController.php", "line": "44"}, "connection": "manufacturing", "explain": null, "start_percent": 39.401, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` in ('planned', 'in_progress') and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "in_progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.638703, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=47", "ajax": false, "filename": "DashboardController.php", "line": "47"}, "connection": "manufacturing", "explain": null, "start_percent": 39.938, "width_percent": 2.727}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and `created_at` >= '2025-07-01 00:00:00' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-07-01 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6456301, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:49", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=49", "ajax": false, "filename": "DashboardController.php", "line": "49"}, "connection": "manufacturing", "explain": null, "start_percent": 42.665, "width_percent": 0.587}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` != 'completed' and `planned_date` < '2025-07-03 00:00:00' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-07-03 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.65006, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:51", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=51", "ajax": false, "filename": "DashboardController.php", "line": "51"}, "connection": "manufacturing", "explain": null, "start_percent": 43.253, "width_percent": 0.579}, {"sql": "select count(*) as aggregate from `manu_raw_materials` where `current_stock` <= `minimum_stock` and `manu_raw_materials`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 54}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.656505, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=54", "ajax": false, "filename": "DashboardController.php", "line": "54"}, "connection": "manufacturing", "explain": null, "start_percent": 43.832, "width_percent": 1.754}, {"sql": "select count(*) as aggregate from `manu_finished_products` where `current_stock` <= `minimum_stock` and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 55}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6639931, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:55", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=55", "ajax": false, "filename": "DashboardController.php", "line": "55"}, "connection": "manufacturing", "explain": null, "start_percent": 45.586, "width_percent": 1.997}, {"sql": "select SUM(current_stock * unit_price) as total from `manu_raw_materials` where `manu_raw_materials`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 56}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.671984, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=56", "ajax": false, "filename": "DashboardController.php", "line": "56"}, "connection": "manufacturing", "explain": null, "start_percent": 47.583, "width_percent": 0.629}, {"sql": "select SUM(current_stock * selling_price) as total from `manu_finished_products` where `manu_finished_products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 57}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6775699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:57", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=57", "ajax": false, "filename": "DashboardController.php", "line": "57"}, "connection": "manufacturing", "explain": null, "start_percent": 48.212, "width_percent": 0.487}, {"sql": "select * from `manu_sales` order by `sale_date` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6828358, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:61", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=61", "ajax": false, "filename": "DashboardController.php", "line": "61"}, "connection": "manufacturing", "explain": null, "start_percent": 48.699, "width_percent": 0.78}, {"sql": "select * from `manu_sale_items` where `manu_sale_items`.`sale_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.691689, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:61", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=61", "ajax": false, "filename": "DashboardController.php", "line": "61"}, "connection": "manufacturing", "explain": null, "start_percent": 49.48, "width_percent": 2.375}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (2, 4, 5, 6, 13) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.699792, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:61", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=61", "ajax": false, "filename": "DashboardController.php", "line": "61"}, "connection": "manufacturing", "explain": null, "start_percent": 51.855, "width_percent": 0.797}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.7061691, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:61", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=61", "ajax": false, "filename": "DashboardController.php", "line": "61"}, "connection": "manufacturing", "explain": null, "start_percent": 52.652, "width_percent": 0.571}, {"sql": "select * from `manu_purchases` order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.7106688, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:63", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=63", "ajax": false, "filename": "DashboardController.php", "line": "63"}, "connection": "manufacturing", "explain": null, "start_percent": 53.223, "width_percent": 0.957}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 63}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.717191, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:63", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=63", "ajax": false, "filename": "DashboardController.php", "line": "63"}, "connection": "manufacturing", "explain": null, "start_percent": 54.179, "width_percent": 0.982}, {"sql": "select * from `manu_manufacturing_orders` where `manu_manufacturing_orders`.`deleted_at` is null order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.724674, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:65", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=65", "ajax": false, "filename": "DashboardController.php", "line": "65"}, "connection": "manufacturing", "explain": null, "start_percent": 55.161, "width_percent": 1.141}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (2, 4, 5) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.732919, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:65", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=65", "ajax": false, "filename": "DashboardController.php", "line": "65"}, "connection": "manufacturing", "explain": null, "start_percent": 56.302, "width_percent": 0.713}, {"sql": "select * from `manu_users` where `manu_users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.738633, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:65", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=65", "ajax": false, "filename": "DashboardController.php", "line": "65"}, "connection": "manufacturing", "explain": null, "start_percent": 57.016, "width_percent": 0.512}, {"sql": "select * from `manu_manufacturing_order_items` where `manu_manufacturing_order_items`.`manufacturing_order_id` in (1, 2, 3, 5, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.744835, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:65", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=65", "ajax": false, "filename": "DashboardController.php", "line": "65"}, "connection": "manufacturing", "explain": null, "start_percent": 57.528, "width_percent": 2.257}, {"sql": "select * from `manu_finished_products` where `manu_finished_products`.`id` in (11, 18) and `manu_finished_products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.752937, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:65", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=65", "ajax": false, "filename": "DashboardController.php", "line": "65"}, "connection": "manufacturing", "explain": null, "start_percent": 59.785, "width_percent": 0.973}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-27'", "type": "query", "params": [], "bindings": ["2025-06-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.758513, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 60.759, "width_percent": 0.545}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-27'", "type": "query", "params": [], "bindings": ["2025-06-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.762888, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 61.304, "width_percent": 0.42}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-28'", "type": "query", "params": [], "bindings": ["2025-06-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.76775, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 61.724, "width_percent": 0.604}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-28'", "type": "query", "params": [], "bindings": ["2025-06-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.772625, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 62.328, "width_percent": 0.453}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-29'", "type": "query", "params": [], "bindings": ["2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.777917, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 62.781, "width_percent": 0.478}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-29'", "type": "query", "params": [], "bindings": ["2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7821772, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 63.259, "width_percent": 0.52}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-30'", "type": "query", "params": [], "bindings": ["2025-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7899039, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 63.78, "width_percent": 0.596}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-06-30'", "type": "query", "params": [], "bindings": ["2025-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7943041, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 64.376, "width_percent": 0.394}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-01'", "type": "query", "params": [], "bindings": ["2025-07-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.798373, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 64.77, "width_percent": 0.621}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-01'", "type": "query", "params": [], "bindings": ["2025-07-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.803909, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 65.391, "width_percent": 0.571}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-02'", "type": "query", "params": [], "bindings": ["2025-07-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.808503, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 65.962, "width_percent": 0.445}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-02'", "type": "query", "params": [], "bindings": ["2025-07-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.814354, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 66.407, "width_percent": 0.554}, {"sql": "select sum(`total_amount`) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-03'", "type": "query", "params": [], "bindings": ["2025-07-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.81978, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:114", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=114", "ajax": false, "filename": "DashboardController.php", "line": "114"}, "connection": "manufacturing", "explain": null, "start_percent": 66.96, "width_percent": 0.881}, {"sql": "select count(*) as aggregate from `manu_sales` where date(`sale_date`) = '2025-07-03'", "type": "query", "params": [], "bindings": ["2025-07-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 68}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.825859, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:118", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=118", "ajax": false, "filename": "DashboardController.php", "line": "118"}, "connection": "manufacturing", "explain": null, "start_percent": 67.842, "width_percent": 0.545}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-06-27' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-06-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.831781, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 68.387, "width_percent": 0.579}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-06-27' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-06-27"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.838213, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 68.966, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-06-28' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-06-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.842748, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 69.503, "width_percent": 0.428}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-06-28' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-06-28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8485, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 69.931, "width_percent": 0.579}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-06-29' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8549452, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 70.51, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-06-29' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-06-29"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8594632, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 71.047, "width_percent": 0.378}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-06-30' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8652952, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 71.425, "width_percent": 0.52}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-06-30' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-06-30"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8709428, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 71.945, "width_percent": 0.638}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-07-01' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-07-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.876452, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 72.583, "width_percent": 0.428}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-07-01' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-07-01"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.881258, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 73.011, "width_percent": 0.621}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-07-02' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-07-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.887609, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 73.632, "width_percent": 0.596}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-07-02' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-07-02"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.89531, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 74.228, "width_percent": 0.487}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'completed' and date(`updated_at`) = '2025-07-03' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["completed", "2025-07-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.899753, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:130", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=130", "ajax": false, "filename": "DashboardController.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 74.715, "width_percent": 0.378}, {"sql": "select count(*) as aggregate from `manu_manufacturing_orders` where `status` = 'planned' and date(`planned_date`) = '2025-07-03' and `manu_manufacturing_orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["planned", "2025-07-03"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 69}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.905055, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:132", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=132", "ajax": false, "filename": "DashboardController.php", "line": "132"}, "connection": "manufacturing", "explain": null, "start_percent": 75.092, "width_percent": 0.462}, {"sql": "select `name`, `current_stock`, `minimum_stock` from `manu_raw_materials` where `is_active` = 1 and `manu_raw_materials`.`deleted_at` is null order by `current_stock` asc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 70}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.910414, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:148", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=148", "ajax": false, "filename": "DashboardController.php", "line": "148"}, "connection": "manufacturing", "explain": null, "start_percent": 75.554, "width_percent": 0.613}, {"sql": "select `manu_finished_products`.`name`, SUM(manu_sale_items.quantity) as total_sold, SUM(manu_sale_items.line_total) as total_revenue from `manu_sale_items` inner join `manu_finished_products` on `manu_sale_items`.`finished_product_id` = `manu_finished_products`.`id` inner join `manu_sales` on `manu_sale_items`.`sale_id` = `manu_sales`.`id` where `manu_sales`.`sale_date` >= '2025-06-03 20:39:09' group by `manu_finished_products`.`id`, `manu_finished_products`.`name` order by `total_revenue` desc limit 5", "type": "query", "params": [], "bindings": ["2025-06-03 20:39:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 175}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 73}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.931623, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:175", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=175", "ajax": false, "filename": "DashboardController.php", "line": "175"}, "connection": "manufacturing", "explain": null, "start_percent": 76.166, "width_percent": 2.383}, {"sql": "select `manu_raw_materials`.`name`, SUM(manu_manufacturing_consumptions.actual_quantity) as total_consumed, SUM(manu_manufacturing_consumptions.total_cost) as total_cost from `manu_manufacturing_consumptions` inner join `manu_raw_materials` on `manu_manufacturing_consumptions`.`raw_material_id` = `manu_raw_materials`.`id` where `manu_manufacturing_consumptions`.`consumed_at` >= '2025-06-03 20:39:09' group by `manu_raw_materials`.`id`, `manu_raw_materials`.`name` order by `total_cost` desc limit 5", "type": "query", "params": [], "bindings": ["2025-06-03 20:39:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 192}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 74}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.9417129, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:192", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\wamp64\\www\\recipe\\app\\Http\\Controllers\\DashboardController.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=192", "ajax": false, "filename": "DashboardController.php", "line": "192"}, "connection": "manufacturing", "explain": null, "start_percent": 78.55, "width_percent": 2.82}, {"sql": "select * from `manu_cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.956766, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "manufacturing", "explain": null, "start_percent": 81.37, "width_percent": 1.813}, {"sql": "delete from `manu_cache` where `key` in ('spatie.permission.cache', 'illuminate:cache:flexible:created:spatie.permission.cache') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["spatie.permission.cache", "illuminate:cache:flexible:created:spatie.permission.cache", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 410}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}], "start": **********.968043, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:410", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 410}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=410", "ajax": false, "filename": "DatabaseStore.php", "line": "410"}, "connection": "manufacturing", "explain": null, "start_percent": 83.182, "width_percent": 0.814}, {"sql": "select * from `manu_permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.975882, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "manufacturing", "explain": null, "start_percent": 83.996, "width_percent": 2.786}, {"sql": "select `manu_roles`.*, `manu_role_has_permissions`.`permission_id` as `pivot_permission_id`, `manu_role_has_permissions`.`role_id` as `pivot_role_id` from `manu_roles` inner join `manu_role_has_permissions` on `manu_roles`.`id` = `manu_role_has_permissions`.`role_id` where `manu_role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.999292, "duration": 0.0049900000000000005, "duration_str": "4.99ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "manufacturing", "explain": null, "start_percent": 86.782, "width_percent": 4.188}, {"sql": "insert into `manu_cache` (`expiration`, `key`, `value`) values (1751661550, 'spatie.permission.cache', 'a:3:{s:5:\\\"alias\\\";a:4:{s:1:\\\"a\\\";s:2:\\\"id\\\";s:1:\\\"b\\\";s:4:\\\"name\\\";s:1:\\\"c\\\";s:10:\\\"guard_name\\\";s:1:\\\"r\\\";s:5:\\\"roles\\\";}s:11:\\\"permissions\\\";a:61:{i:0;a:4:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:14:\\\"view-dashboard\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:7:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:8;}}i:1;a:4:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:14:\\\"view-analytics\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:2;a:4:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:10:\\\"view-users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:3;a:4:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:12:\\\"create-users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:4;a:4:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:10:\\\"edit-users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:5;a:4:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:12:\\\"delete-users\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:12:\\\"assign-roles\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:7;a:4:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:10:\\\"view-units\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:8;a:4:{s:1:\\\"a\\\";i:9;s:1:\\\"b\\\";s:12:\\\"create-units\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:9;a:4:{s:1:\\\"a\\\";i:10;s:1:\\\"b\\\";s:10:\\\"edit-units\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:10;a:4:{s:1:\\\"a\\\";i:11;s:1:\\\"b\\\";s:12:\\\"delete-units\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:11;a:4:{s:1:\\\"a\\\";i:12;s:1:\\\"b\\\";s:18:\\\"view-raw-materials\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:6:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:6;i:5;i:8;}}i:12;a:4:{s:1:\\\"a\\\";i:13;s:1:\\\"b\\\";s:20:\\\"create-raw-materials\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:13;a:4:{s:1:\\\"a\\\";i:14;s:1:\\\"b\\\";s:18:\\\"edit-raw-materials\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:14;a:4:{s:1:\\\"a\\\";i:15;s:1:\\\"b\\\";s:20:\\\"delete-raw-materials\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:15;a:4:{s:1:\\\"a\\\";i:16;s:1:\\\"b\\\";s:28:\\\"view-raw-materials-movements\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:16;a:4:{s:1:\\\"a\\\";i:17;s:1:\\\"b\\\";s:30:\\\"create-raw-materials-movements\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:17;a:4:{s:1:\\\"a\\\";i:18;s:1:\\\"b\\\";s:22:\\\"view-finished-products\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:8:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:7;i:7;i:8;}}i:18;a:4:{s:1:\\\"a\\\";i:19;s:1:\\\"b\\\";s:24:\\\"create-finished-products\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:19;a:4:{s:1:\\\"a\\\";i:20;s:1:\\\"b\\\";s:22:\\\"edit-finished-products\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:20;a:4:{s:1:\\\"a\\\";i:21;s:1:\\\"b\\\";s:24:\\\"delete-finished-products\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:21;a:4:{s:1:\\\"a\\\";i:22;s:1:\\\"b\\\";s:32:\\\"view-finished-products-movements\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:22;a:4:{s:1:\\\"a\\\";i:23;s:1:\\\"b\\\";s:34:\\\"create-finished-products-movements\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:23;a:4:{s:1:\\\"a\\\";i:24;s:1:\\\"b\\\";s:14:\\\"manage-recipes\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:24;a:4:{s:1:\\\"a\\\";i:25;s:1:\\\"b\\\";s:25:\\\"view-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;i:4;i:8;}}i:25;a:4:{s:1:\\\"a\\\";i:26;s:1:\\\"b\\\";s:27:\\\"create-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:26;a:4:{s:1:\\\"a\\\";i:27;s:1:\\\"b\\\";s:25:\\\"edit-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:27;a:4:{s:1:\\\"a\\\";i:28;s:1:\\\"b\\\";s:27:\\\"delete-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:28;a:4:{s:1:\\\"a\\\";i:29;s:1:\\\"b\\\";s:26:\\\"start-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:29;a:4:{s:1:\\\"a\\\";i:30;s:1:\\\"b\\\";s:29:\\\"complete-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:30;a:4:{s:1:\\\"a\\\";i:31;s:1:\\\"b\\\";s:27:\\\"cancel-manufacturing-orders\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:31;a:4:{s:1:\\\"a\\\";i:32;s:1:\\\"b\\\";s:31:\\\"view-manufacturing-consumptions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:32;a:4:{s:1:\\\"a\\\";i:33;s:1:\\\"b\\\";s:33:\\\"update-manufacturing-consumptions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:33;a:4:{s:1:\\\"a\\\";i:34;s:1:\\\"b\\\";s:14:\\\"view-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:8;}}i:34;a:4:{s:1:\\\"a\\\";i:35;s:1:\\\"b\\\";s:16:\\\"create-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:35;a:4:{s:1:\\\"a\\\";i:36;s:1:\\\"b\\\";s:14:\\\"edit-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:36;a:4:{s:1:\\\"a\\\";i:37;s:1:\\\"b\\\";s:16:\\\"delete-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:37;a:4:{s:1:\\\"a\\\";i:38;s:1:\\\"b\\\";s:17:\\\"approve-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:38;a:4:{s:1:\\\"a\\\";i:39;s:1:\\\"b\\\";s:17:\\\"receive-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:39;a:4:{s:1:\\\"a\\\";i:40;s:1:\\\"b\\\";s:18:\\\"complete-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:40;a:4:{s:1:\\\"a\\\";i:41;s:1:\\\"b\\\";s:16:\\\"cancel-purchases\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:2:{i:0;i:1;i:1;i:2;}}i:41;a:4:{s:1:\\\"a\\\";i:42;s:1:\\\"b\\\";s:24:\\\"view-purchase-statistics\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:42;a:4:{s:1:\\\"a\\\";i:43;s:1:\\\"b\\\";s:10:\\\"access-pos\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:43;a:4:{s:1:\\\"a\\\";i:44;s:1:\\\"b\\\";s:13:\\\"process-sales\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:44;a:4:{s:1:\\\"a\\\";i:45;s:1:\\\"b\\\";s:18:\\\"view-sales-history\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:5:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;i:4;i:8;}}i:45;a:4:{s:1:\\\"a\\\";i:46;s:1:\\\"b\\\";s:17:\\\"hold-transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:46;a:4:{s:1:\\\"a\\\";i:47;s:1:\\\"b\\\";s:26:\\\"retrieve-held-transactions\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:47;a:4:{s:1:\\\"a\\\";i:48;s:1:\\\"b\\\";s:10:\\\"void-sales\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:48;a:4:{s:1:\\\"a\\\";i:49;s:1:\\\"b\\\";s:15:\\\"apply-discounts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:49;a:4:{s:1:\\\"a\\\";i:50;s:1:\\\"b\\\";s:22:\\\"view-inventory-reports\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:8;}}i:50;a:4:{s:1:\\\"a\\\";i:51;s:1:\\\"b\\\";s:16:\\\"adjust-inventory\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:51;a:4:{s:1:\\\"a\\\";i:52;s:1:\\\"b\\\";s:21:\\\"view-low-stock-alerts\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:6;}}i:52;a:4:{s:1:\\\"a\\\";i:53;s:1:\\\"b\\\";s:22:\\\"manage-stock-movements\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:53;a:4:{s:1:\\\"a\\\";i:54;s:1:\\\"b\\\";s:18:\\\"view-sales-reports\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:8;}}i:54;a:4:{s:1:\\\"a\\\";i:55;s:1:\\\"b\\\";s:23:\\\"view-production-reports\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:8;}}i:55;a:4:{s:1:\\\"a\\\";i:56;s:1:\\\"b\\\";s:22:\\\"view-financial-reports\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:56;a:4:{s:1:\\\"a\\\";i:57;s:1:\\\"b\\\";s:14:\\\"export-reports\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;}}i:57;a:4:{s:1:\\\"a\\\";i:58;s:1:\\\"b\\\";s:22:\\\"manage-system-settings\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:58;a:4:{s:1:\\\"a\\\";i:59;s:1:\\\"b\\\";s:16:\\\"view-system-logs\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\\\"a\\\";i:60;s:1:\\\"b\\\";s:13:\\\"backup-system\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\\\"a\\\";i:61;s:1:\\\"b\\\";s:14:\\\"restore-system\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";s:1:\\\"r\\\";a:1:{i:0;i:1;}}}s:5:\\\"roles\\\";a:8:{i:0;a:3:{s:1:\\\"a\\\";i:1;s:1:\\\"b\\\";s:11:\\\"Super Admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:1;a:3:{s:1:\\\"a\\\";i:2;s:1:\\\"b\\\";s:5:\\\"Admin\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:2;a:3:{s:1:\\\"a\\\";i:3;s:1:\\\"b\\\";s:18:\\\"Production Manager\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:3;a:3:{s:1:\\\"a\\\";i:4;s:1:\\\"b\\\";s:17:\\\"Inventory Manager\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:4;a:3:{s:1:\\\"a\\\";i:5;s:1:\\\"b\\\";s:13:\\\"Sales Manager\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:5;a:3:{s:1:\\\"a\\\";i:6;s:1:\\\"b\\\";s:19:\\\"Production Operator\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:6;a:3:{s:1:\\\"a\\\";i:8;s:1:\\\"b\\\";s:6:\\\"Viewer\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}i:7;a:3:{s:1:\\\"a\\\";i:7;s:1:\\\"b\\\";s:7:\\\"Cashier\\\";s:1:\\\"c\\\";s:3:\\\"web\\\";}}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1751661550, "spatie.permission.cache", "a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:61:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:14:\"view-dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:7:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:8;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:14:\"view-analytics\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:10:\"view-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:12:\"create-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:10:\"edit-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:12:\"delete-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:12:\"assign-roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:10:\"view-units\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:12:\"create-units\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:10:\"edit-units\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:12:\"delete-units\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:18:\"view-raw-materials\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:6:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:6;i:5;i:8;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:20:\"create-raw-materials\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:18:\"edit-raw-materials\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:20:\"delete-raw-materials\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:28:\"view-raw-materials-movements\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:30:\"create-raw-materials-movements\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:22:\"view-finished-products\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:8:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:7;i:7;i:8;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:24:\"create-finished-products\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:22:\"edit-finished-products\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:24:\"delete-finished-products\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:32:\"view-finished-products-movements\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:34:\"create-finished-products-movements\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:14:\"manage-recipes\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:25:\"view-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;i:4;i:8;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:27:\"create-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:25:\"edit-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:27:\"delete-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:26:\"start-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:29:\"complete-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:27:\"cancel-manufacturing-orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:31:\"view-manufacturing-consumptions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:33:\"update-manufacturing-consumptions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:6;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:14:\"view-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:8;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:16:\"create-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:14:\"edit-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:16:\"delete-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:17:\"approve-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:17:\"receive-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:18:\"complete-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:16:\"cancel-purchases\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:24:\"view-purchase-statistics\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:10:\"access-pos\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:13:\"process-sales\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:18:\"view-sales-history\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;i:4;i:8;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:17:\"hold-transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:26:\"retrieve-held-transactions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:7;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:10:\"void-sales\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:15:\"apply-discounts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:22:\"view-inventory-reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:8;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:16:\"adjust-inventory\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:21:\"view-low-stock-alerts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:6;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:22:\"manage-stock-movements\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:18:\"view-sales-reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:5;i:3;i:8;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:23:\"view-production-reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:8;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:22:\"view-financial-reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:14:\"export-reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:22:\"manage-system-settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:16:\"view-system-logs\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:13:\"backup-system\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:14:\"restore-system\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}}s:5:\"roles\";a:8:{i:0;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:11:\"Super Admin\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:2;s:1:\"b\";s:5:\"Admin\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:3;s:1:\"b\";s:18:\"Production Manager\";s:1:\"c\";s:3:\"web\";}i:3;a:3:{s:1:\"a\";i:4;s:1:\"b\";s:17:\"Inventory Manager\";s:1:\"c\";s:3:\"web\";}i:4;a:3:{s:1:\"a\";i:5;s:1:\"b\";s:13:\"Sales Manager\";s:1:\"c\";s:3:\"web\";}i:5;a:3:{s:1:\"a\";i:6;s:1:\"b\";s:19:\"Production Operator\";s:1:\"c\";s:3:\"web\";}i:6;a:3:{s:1:\"a\";i:8;s:1:\"b\";s:6:\"Viewer\";s:1:\"c\";s:3:\"web\";}i:7;a:3:{s:1:\"a\";i:7;s:1:\"b\";s:7:\"Cashier\";s:1:\"c\";s:3:\"web\";}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 190}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 167}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 237}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 429}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.1134288, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:190", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=190", "ajax": false, "filename": "DatabaseStore.php", "line": "190"}, "connection": "manufacturing", "explain": null, "start_percent": 90.97, "width_percent": 1.972}, {"sql": "select `manu_permissions`.*, `manu_model_has_permissions`.`model_id` as `pivot_model_id`, `manu_model_has_permissions`.`permission_id` as `pivot_permission_id`, `manu_model_has_permissions`.`model_type` as `pivot_model_type` from `manu_permissions` inner join `manu_model_has_permissions` on `manu_permissions`.`id` = `manu_model_has_permissions`.`permission_id` where `manu_model_has_permissions`.`model_id` in (1) and `manu_model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.1312149, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "manufacturing", "explain": null, "start_percent": 92.942, "width_percent": 1.796}, {"sql": "select `manu_roles`.*, `manu_model_has_roles`.`model_id` as `pivot_model_id`, `manu_model_has_roles`.`role_id` as `pivot_role_id`, `manu_model_has_roles`.`model_type` as `pivot_model_type` from `manu_roles` inner join `manu_model_has_roles` on `manu_roles`.`id` = `manu_model_has_roles`.`role_id` where `manu_model_has_roles`.`model_id` in (1) and `manu_model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.142065, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "manufacturing", "explain": null, "start_percent": 94.738, "width_percent": 1.586}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 71}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.175854, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 96.324, "width_percent": 1.041}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 130}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.182207, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 97.365, "width_percent": 0.436}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 142}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.188331, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 97.801, "width_percent": 0.495}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 292}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.194128, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 98.296, "width_percent": 0.378}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 371}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1994631, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 98.674, "width_percent": 0.378}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 371}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.205971, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 99.052, "width_percent": 0.428}, {"sql": "select * from `manu_currency_settings` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Helpers/Helpers.php", "file": "C:\\wamp64\\www\\recipe\\app\\Helpers\\Helpers.php", "line": 236}, {"index": 18, "namespace": "view", "name": "content.dashboard.dashboards-analytics", "file": "C:\\wamp64\\www\\recipe\\resources\\views/content/dashboard/dashboards-analytics.blade.php", "line": 371}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\recipe\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.210755, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CurrencySetting.php:31", "source": {"index": 16, "namespace": null, "name": "app/Models/CurrencySetting.php", "file": "C:\\wamp64\\www\\recipe\\app\\Models\\CurrencySetting.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FCurrencySetting.php&line=31", "ajax": false, "filename": "CurrencySetting.php", "line": "31"}, "connection": "manufacturing", "explain": null, "start_percent": 99.48, "width_percent": 0.52}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 193, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 61, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\RawMaterial": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FRawMaterial.php&line=1", "ajax": false, "filename": "RawMaterial.php", "line": "?"}}, "App\\Models\\FinishedProduct": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FFinishedProduct.php&line=1", "ajax": false, "filename": "FinishedProduct.php", "line": "?"}}, "App\\Models\\SaleItem": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FSaleItem.php&line=1", "ajax": false, "filename": "SaleItem.php", "line": "?"}}, "App\\Models\\Purchase": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FPurchase.php&line=1", "ajax": false, "filename": "Purchase.php", "line": "?"}}, "App\\Models\\ManufacturingOrder": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrder.php&line=1", "ajax": false, "filename": "ManufacturingOrder.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Sale": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FSale.php&line=1", "ajax": false, "filename": "Sale.php", "line": "?"}}, "App\\Models\\ManufacturingOrderItem": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FModels%2FManufacturingOrderItem.php&line=1", "ajax": false, "filename": "ManufacturingOrderItem.php", "line": "?"}}}, "count": 300, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 38, "messages": [{"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-614673926 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614673926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148832, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-252981716 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252981716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.151708, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1279631031 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279631031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.154166, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1907644753 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907644753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156148, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1855598688 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855598688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.158163, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-12330063 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12330063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160419, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465029854 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465029854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161785, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947180892 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947180892\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.162862, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2119034121 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119034121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170535, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-836563698 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836563698\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223719, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1317351675 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317351675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225039, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085886841 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085886841\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.226451, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2035873612 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035873612\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227778, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-748688650 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748688650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22953, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-439146104 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439146104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.231406, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-749573455 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749573455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.232634, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947989157 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947989157\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.233673, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1421552528 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421552528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.240353, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-74794110 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74794110\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.24218, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-296652160 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296652160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.243833, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1852101201 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852101201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245623, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1388231070 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388231070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.246932, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-864803097 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864803097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.248319, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1050501145 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050501145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.249989, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-185989648 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185989648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.251897, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-10102161 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10102161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.253577, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1713238731 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713238731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2595, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273787294 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273787294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.261757, "xdebug_link": null}, {"message": "[\n  ability => view-raw-materials,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-839316149 data-indent-pad=\"  \"><span class=sf-dump-note>view-raw-materials </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view-raw-materials</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839316149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262989, "xdebug_link": null}, {"message": "[\n  ability => view-finished-products,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286305895 data-indent-pad=\"  \"><span class=sf-dump-note>view-finished-products </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">view-finished-products</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286305895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264269, "xdebug_link": null}, {"message": "[\n  ability => view-manufacturing-orders,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-947412146 data-indent-pad=\"  \"><span class=sf-dump-note>view-manufacturing-orders </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view-manufacturing-orders</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947412146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.266124, "xdebug_link": null}, {"message": "[\n  ability => view-purchases,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1553153247 data-indent-pad=\"  \"><span class=sf-dump-note>view-purchases </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view-purchases</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553153247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2678, "xdebug_link": null}, {"message": "[\n  ability => access-pos,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-455740446 data-indent-pad=\"  \"><span class=sf-dump-note>access-pos </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">access-pos</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455740446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269698, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1118962455 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118962455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27062, "xdebug_link": null}, {"message": "[\n  ability => view-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-876736227 data-indent-pad=\"  \"><span class=sf-dump-note>view-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876736227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271577, "xdebug_link": null}, {"message": "[\n  ability => view-roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1992778535 data-indent-pad=\"  \"><span class=sf-dump-note>view-roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992778535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277081, "xdebug_link": null}, {"message": "[\n  ability => manage-settings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382807337 data-indent-pad=\"  \"><span class=sf-dump-note>manage-settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382807337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282799, "xdebug_link": null}, {"message": "[\n  ability => view-units,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1995394479 data-indent-pad=\"  \"><span class=sf-dump-note>view-units </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view-units</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995394479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.283677, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\DashboardController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Frecipe%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:17-23</a>", "middleware": "web, auth", "duration": "895ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-937823495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-937823495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1930566101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1930566101\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1639528026 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1583 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkpIVXNSeFY4T3JibzJROGxqY3RFVXc9PSIsInZhbHVlIjoiRGkzNThROU5ueUZubmN5UzJ5S2VnL25JUHFLa2o5L2FFc1ZCa1RsMnBSeWJUejlEVmRhcGtUYWpSWHpXTVlKdVNLNmlOKzN4R0hnTkExZ0MwSTMvTFVEcjh1cnJNQkJteW9LUFAvbzVYUlBwc1ltbzBxdkg5cnArTDRsSEVXZVUiLCJtYWMiOiI3MDY0NTkyMGY2NTdjYWVmOTc1N2E2NjQxMzc2NTllMjRkNDgzMDE0ZjAwYzQyMTlhNmNiNjhhYTJjOTJkNDk2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImNYVkZ4WnhGTXFSZVcramlIODdPSkE9PSIsInZhbHVlIjoiaDA3Sm91WTI5endBYXc3c3huYWZnWGd6bjZhcnJ6eHBrUHlaV2dzeG1rQUd0TStPa1RyMmVwaThCQS9tR1I1TC9JeG11SVd2dTV5VnNiZ1hKVU54VUNZcVZUMlR3cE05cXVKSGJDY1ZIY0d2bU52ZUFYQVlLTmZDMUYwUWZNODIiLCJtYWMiOiI0YmFjYTFkMmI1ZmM0YWQxNzViMjhkOGMxMGNmNmM3OWUwNTA1ZWFjOTg0MGNmOGMyNzNiNWU1MjdmYjliM2E5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639528026\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-694251533 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6EbpdNMCJZBpmlmSlPqCQpq9gzFHUcsr1NCNCi5L</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H6ofDm7SW67K2M9V0u4VIwPstUUnmPZaf2FGs6IS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694251533\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1969574760 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 20:39:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969574760\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1959281244 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6EbpdNMCJZBpmlmSlPqCQpq9gzFHUcsr1NCNCi5L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ907ETG16SAQDSS88YCHM69</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959281244\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\DashboardController@index"}, "badge": null}}